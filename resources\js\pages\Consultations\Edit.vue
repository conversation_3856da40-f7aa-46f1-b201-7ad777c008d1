<template>
    <AppLayout>
        <Head title="Edit Consultation" />

        <DynamicConsultationForm :consultation-id="consultationId" />
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import DynamicConsultationForm from '@/components/consultation/DynamicConsultationForm.vue'

interface Props {
    consultationId: string
}

const props = defineProps<Props>()
</script>
