<template>
  <AppLayout>
    <Head :title="isEditing ? 'Edit Consultation' : 'New Consultation'" />

    <div class="min-h-screen bg-gray-50">
      <!-- Header -->
      <div class="bg-white border-b border-gray-200 px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <Button variant="ghost" @click="router.visit('/consultations')" class="text-gray-600">
              <ArrowLeft class="w-4 h-4 mr-2" />
              Back
            </Button>
            <div v-if="consultation?.patient">
              <h1 class="text-xl font-semibold text-gray-900">
                {{ consultation.patient.user?.name || 'Unknown Patient' }}
              </h1>
              <p class="text-sm text-gray-600">
                {{ formatDate(consultation.consultation_date) }}
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <Button variant="outline" size="sm">
              <FileText class="w-4 h-4 mr-2" />
              Export Record
            </Button>
            <Button variant="outline" size="sm">
              <Printer class="w-4 h-4 mr-2" />
              Print
            </Button>
            <Button @click="saveConsultation" :disabled="saving" size="sm" class="bg-blue-600 hover:bg-blue-700">
              <Loader2 v-if="saving" class="w-4 h-4 mr-2 animate-spin" />
              {{ saving ? 'Saving...' : 'Save & Continue' }}
            </Button>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="templateLoading" class="flex items-center justify-center py-12">
        <Loader2 class="w-8 h-8 animate-spin text-blue-600" />
        <span class="ml-2 text-gray-600">Loading consultation template...</span>
      </div>

      <!-- Main Content -->
      <div v-else class="flex-1 px-6 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <!-- Left Sidebar - Patient Info & Vital Signs -->
          <div class="lg:col-span-1 space-y-6">
            <!-- Patient Information Card -->
            <Card>
              <CardHeader>
                <CardTitle class="text-lg flex items-center">
                  <User class="w-5 h-5 mr-2" />
                  Patient Information
                </CardTitle>
              </CardHeader>
              <CardContent v-if="consultation?.patient" class="space-y-3">
                <div>
                  <Label class="text-sm font-medium text-gray-500">Name</Label>
                  <p class="font-medium">{{ consultation.patient.user?.name }}</p>
                </div>
                <div>
                  <Label class="text-sm font-medium text-gray-500">Age</Label>
                  <p>{{ calculateAge(consultation.patient.date_of_birth) }} years</p>
                </div>
                <div>
                  <Label class="text-sm font-medium text-gray-500">Gender</Label>
                  <p>{{ consultation.patient.user?.gender || 'Not specified' }}</p>
                </div>
                <div>
                  <Label class="text-sm font-medium text-gray-500">Phone</Label>
                  <p>{{ consultation.patient.phone || 'Not provided' }}</p>
                </div>
              </CardContent>
            </Card>

            <!-- Vital Signs Card -->
            <VitalSignsCard
              :vital-signs="consultationData.vital_signs"
              :template="template?.vital_signs"
              @update="updateVitalSigns"
            />

            <!-- Previous Visits Card -->
            <PreviousVisitsCard :patient-id="consultation?.patient_id" />
          </div>

          <!-- Middle Column - Main Consultation Tabs -->
          <div class="lg:col-span-2 space-y-6">
            <!-- Main Tabs -->
            <ConsultationMainTabs
              :main-tabs="consultationData.main_tabs"
              :template="template?.main_tabs"
              @update="updateMainTabs"
              @add-entry="addMainTabEntry"
              @remove-entry="removeMainTabEntry"
            />

            <!-- Additional Tabs -->
            <ConsultationAdditionalTabs
              :additional-tabs="consultationData.additional_tabs"
              :template="template?.additional_tabs"
              @update="updateAdditionalTabs"
              @add-tab="addAdditionalTab"
              @remove-tab="removeAdditionalTab"
              @add-entry="addAdditionalTabEntry"
              @remove-entry="removeAdditionalTabEntry"
            />
          </div>

          <!-- Right Column - Actions & History -->
          <div class="lg:col-span-1 space-y-6">
            <!-- Quick Actions -->
            <QuickActionsCard
              :consultation-id="consultationId"
              @add-prescription="showPrescriptionModal = true"
              @add-document="showDocumentModal = true"
            />

            <!-- Recent Activity -->
            <RecentActivityCard :consultation="consultation" />
          </div>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <PrescriptionModal
      v-if="showPrescriptionModal"
      :consultation-id="consultationId"
      @close="showPrescriptionModal = false"
      @saved="loadConsultation"
    />

    <DocumentUploadModal
      v-if="showDocumentModal"
      :consultation-id="consultationId"
      @close="showDocumentModal = false"
      @uploaded="loadConsultation"
    />
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import {
  ArrowLeft, FileText, Printer, User, Loader2
} from 'lucide-vue-next'

// Import consultation components
import VitalSignsCard from '@/components/consultation/VitalSignsCard.vue'
import ConsultationMainTabs from '@/components/consultation/ConsultationMainTabs.vue'
import ConsultationAdditionalTabs from '@/components/consultation/ConsultationAdditionalTabs.vue'
import PrescriptionModal from '@/components/consultation/PrescriptionModal.vue'
import DocumentUploadModal from '@/components/consultation/DocumentUploadModal.vue'
import QuickActionsCard from '@/components/consultation/QuickActionsCard.vue'
import RecentActivityCard from '@/components/consultation/RecentActivityCard.vue'
import PreviousVisitsCard from '@/components/consultation/PreviousVisitsCard.vue'

// Import composables
import { useConsultationTemplate } from '@/composables/useConsultationTemplate'
import { useConsultationApi } from '@/composables/useConsultationApi'
import axios from 'axios'

interface Props {
  consultationId?: string
  appointmentId?: string
}

const props = defineProps<Props>()

// Template and consultation data
const {
  template,
  loading: templateLoading,
  loadTemplate,
  initializeConsultationData,
  addTabEntry,
  removeTabEntry,
  calculateBMI,
  validateConsultationData
} = useConsultationTemplate()

const {
  getConsultation,
  updateConsultation,
  createConsultation
} = useConsultationApi()

// Component state
const consultation = ref(null)
const consultationData = ref({
  vital_signs: {},
  main_tabs: {},
  additional_tabs: {}
})
const saving = ref(false)
const showPrescriptionModal = ref(false)
const showDocumentModal = ref(false)

// Computed
const isEditing = computed(() => !!props.consultationId)

// Methods
const loadConsultation = async () => {
  if (props.consultationId) {
    try {
      const response = await getConsultation(props.consultationId)
      consultation.value = response.data
      consultationData.value = {
        vital_signs: response.data.vital_signs || {},
        main_tabs: response.data.main_tabs || {},
        additional_tabs: response.data.additional_tabs || {}
      }
    } catch (error) {
      console.error('Error loading consultation:', error)
    }
  } else if (props.appointmentId) {
    try {
      // Load appointment data to pre-populate consultation
      const response = await axios.get(`/api/appointments/${props.appointmentId}`)
      const appointment = response.data.data

      // Create consultation structure with appointment data
      consultation.value = {
        appointment_id: appointment.id,
        patient: appointment.patient,
        provider: appointment.provider,
        consultation_date: appointment.appointment_date,
        consultation_mode: appointment.appointment_type === 'video' ? 'video' : 'in_person',
        is_telemedicine: appointment.appointment_type === 'video'
      }

      consultationData.value = initializeConsultationData()
    } catch (error) {
      console.error('Error loading appointment:', error)
      consultationData.value = initializeConsultationData()
    }
  } else {
    // Initialize new consultation
    consultationData.value = initializeConsultationData()
  }
}

const saveConsultation = async () => {
  try {
    saving.value = true

    // Calculate BMI if height and weight are provided
    calculateBMI(consultationData.value)

    // Validate data
    const errors = validateConsultationData(consultationData.value)
    if (errors.length > 0) {
      alert('Please fix the following errors:\n' + errors.join('\n'))
      return
    }

    const payload = {
      ...consultationData.value,
      status: 'in_progress',
      // Include appointment data if creating from appointment
      ...(props.appointmentId && {
        appointment_id: props.appointmentId,
        patient_id: consultation.value?.patient?.id,
        consultation_date: consultation.value?.consultation_date,
        consultation_mode: consultation.value?.consultation_mode,
        is_telemedicine: consultation.value?.is_telemedicine
      })
    }

    if (isEditing.value) {
      await updateConsultation(props.consultationId, payload)
    } else {
      const response = await createConsultation(payload)
      router.visit(`/consultations/${response.data.id}/edit`)
    }
  } catch (error) {
    console.error('Error saving consultation:', error)
  } finally {
    saving.value = false
  }
}

// Helper methods
const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

const calculateAge = (birthDate) => {
  if (!birthDate) return 'Unknown'
  const today = new Date()
  const birth = new Date(birthDate)
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }
  return age
}

// Event handlers for child components
const updateVitalSigns = (vitalSigns) => {
  consultationData.value.vital_signs = vitalSigns
}

const updateMainTabs = (mainTabs) => {
  consultationData.value.main_tabs = mainTabs
}

const updateAdditionalTabs = (additionalTabs) => {
  consultationData.value.additional_tabs = additionalTabs
}

const addMainTabEntry = (tabKey, content) => {
  addTabEntry(consultationData.value, 'main_tabs', tabKey, content)
}

const removeMainTabEntry = (tabKey, entryId) => {
  removeTabEntry(consultationData.value, 'main_tabs', tabKey, entryId)
}

const addAdditionalTab = (tabKey) => {
  if (!consultationData.value.additional_tabs[tabKey]) {
    consultationData.value.additional_tabs[tabKey] = []
  }
}

const removeAdditionalTab = (tabKey) => {
  delete consultationData.value.additional_tabs[tabKey]
}

const addAdditionalTabEntry = (tabKey, content) => {
  addTabEntry(consultationData.value, 'additional_tabs', tabKey, content)
}

const removeAdditionalTabEntry = (tabKey, entryId) => {
  removeTabEntry(consultationData.value, 'additional_tabs', tabKey, entryId)
}

// Initialize
onMounted(async () => {
  await loadTemplate()
  await loadConsultation()
})
</script>
