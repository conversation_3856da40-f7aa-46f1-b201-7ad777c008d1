<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('consultation_vitals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('consultation_id')->constrained()->onDelete('cascade');
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->string('instance_id')->nullable(); // For tracking multiple vital sign instances
            $table->decimal('temperature', 5, 2)->nullable();
            $table->string('temperature_unit')->default('F'); // F or C
            $table->integer('pulse')->nullable();
            $table->integer('systolic_bp')->nullable();
            $table->integer('diastolic_bp')->nullable();
            $table->integer('respiratory_rate')->nullable();
            $table->integer('oxygen_saturation')->nullable();
            $table->decimal('weight', 8, 2)->nullable();
            $table->string('weight_unit')->default('kg'); // kg or lbs
            $table->decimal('height', 8, 2)->nullable();
            $table->string('height_unit')->default('cm'); // cm or inches
            $table->decimal('bmi', 5, 2)->nullable();
            $table->integer('pain_level')->nullable(); // 1-10 scale

            // Additional KiviCare vital signs
            $table->integer('heart_rate')->nullable(); // beats per minute
            $table->decimal('glucose_level', 8, 2)->nullable();
            $table->string('glucose_unit')->default('mg/dL'); // mg/dL or mmol/L
            $table->decimal('cholesterol', 8, 2)->nullable();
            $table->decimal('blood_sugar', 8, 2)->nullable();
            $table->string('blood_group')->nullable();
            $table->decimal('head_circumference', 8, 2)->nullable(); // for pediatric
            $table->decimal('waist_circumference', 8, 2)->nullable();

            // Clinical observations
            $table->text('general_appearance')->nullable();
            $table->text('skin_condition')->nullable();
            $table->text('mental_status')->nullable();
            $table->text('notes')->nullable();

            // Metadata (KiviCare style)
            $table->json('additional_data')->nullable(); // For storing any additional vital sign data
            $table->boolean('is_ai_populated')->default(false);
            $table->boolean('is_approved')->default(false);
            $table->datetime('recorded_at')->nullable();
            $table->foreignId('recorded_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();

            // Indexes for better performance
            $table->index(['consultation_id', 'created_at']);
            $table->index(['patient_id', 'created_at']);
            $table->index(['instance_id']);
            $table->index(['recorded_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('consultation_vitals');
    }
};