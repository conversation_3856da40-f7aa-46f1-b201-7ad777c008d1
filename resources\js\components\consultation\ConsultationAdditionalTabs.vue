<template>
  <div class="space-y-6">
    <!-- Additional Tabs Header -->
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold">Additional Sections</h3>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <Plus class="w-4 h-4 mr-1" />
            Add Section
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" class="w-56">
          <DropdownMenuItem
            v-for="(field, key) in availableAdditionalTabs"
            :key="key"
            @click="addTab(key)"
            class="flex items-center"
          >
            <component :is="getIcon(field.icon)" class="w-4 h-4 mr-2" />
            {{ field.label }}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>

    <!-- Active Additional Tabs -->
    <div v-if="Object.keys(additionalTabs).length" class="space-y-4">
      <div v-for="(entries, tabKey) in additionalTabs" :key="tabKey">
        <Card>
          <CardHeader>
            <CardTitle class="text-lg flex items-center justify-between">
              <div class="flex items-center">
                <component :is="getIcon(template[tabKey]?.icon)" class="w-5 h-5 mr-2" />
                {{ template[tabKey]?.label || tabKey }}
              </div>
              <div class="flex items-center space-x-2">
                <Button variant="ghost" size="sm" @click="addEntry(tabKey)">
                  <Plus class="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm" @click="removeTab(tabKey)">
                  <X class="w-4 h-4 text-red-500" />
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <!-- Existing Entries -->
            <div v-if="entries?.length" class="space-y-3">
              <div 
                v-for="(entry, index) in entries" 
                :key="entry.id"
                class="relative group"
              >
                <div class="flex items-start space-x-3">
                  <div class="flex-1">
                    <Textarea
                      :placeholder="template[tabKey]?.placeholder || `Enter ${template[tabKey]?.label || tabKey}...`"
                      :value="entry.content"
                      @input="updateEntry(tabKey, entry.id, $event.target.value)"
                      class="min-h-[80px] resize-none"
                    />
                    <div class="text-xs text-gray-500 mt-1">
                      {{ formatDate(entry.created_at) }}
                    </div>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    @click="removeEntry(tabKey, entry.id)"
                    class="opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <X class="w-4 h-4 text-red-500" />
                  </Button>
                </div>
              </div>
            </div>

            <!-- Empty State -->
            <div v-else class="text-center py-6 text-gray-500">
              <div class="text-sm">{{ template[tabKey]?.placeholder || `No ${template[tabKey]?.label || tabKey} added yet` }}</div>
              <Button variant="outline" size="sm" @click="addEntry(tabKey)" class="mt-2">
                <Plus class="w-4 h-4 mr-1" />
                Add Entry
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- Empty State for Additional Tabs -->
    <div v-else class="text-center py-8 text-gray-500">
      <div class="text-sm mb-4">No additional sections added yet</div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline">
            <Plus class="w-4 h-4 mr-1" />
            Add Your First Section
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="center" class="w-56">
          <DropdownMenuItem
            v-for="(field, key) in availableAdditionalTabs"
            :key="key"
            @click="addTab(key)"
            class="flex items-center"
          >
            <component :is="getIcon(field.icon)" class="w-4 h-4 mr-2" />
            {{ field.label }}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { 
  Plus, X, AlertTriangle, Users, User, FileText, Pill, 
  List, Shield, LifeBuoy, Heart, Brain, Activity, Edit
} from 'lucide-vue-next'

interface TabEntry {
  id: number
  content: string
  created_at: string
}

interface Props {
  additionalTabs: Record<string, TabEntry[]>
  template: Record<string, any>
}

interface Emits {
  (e: 'update', additionalTabs: Record<string, TabEntry[]>): void
  (e: 'add-tab', tabKey: string): void
  (e: 'remove-tab', tabKey: string): void
  (e: 'add-entry', tabKey: string, content: string): void
  (e: 'remove-entry', tabKey: string, entryId: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Computed
const availableAdditionalTabs = computed(() => {
  if (!props.template) return {}
  
  // Filter out tabs that are already active
  return Object.fromEntries(
    Object.entries(props.template).filter(([key]) => !props.additionalTabs[key])
  )
})

// Methods
const addTab = (tabKey: string) => {
  emit('add-tab', tabKey)
}

const removeTab = (tabKey: string) => {
  emit('remove-tab', tabKey)
}

const addEntry = (tabKey: string) => {
  emit('add-entry', tabKey, '')
}

const removeEntry = (tabKey: string, entryId: number) => {
  emit('remove-entry', tabKey, entryId)
}

const updateEntry = (tabKey: string, entryId: number, content: string) => {
  const updatedAdditionalTabs = { ...props.additionalTabs }
  if (updatedAdditionalTabs[tabKey]) {
    const entry = updatedAdditionalTabs[tabKey].find(e => e.id === entryId)
    if (entry) {
      entry.content = content
      emit('update', updatedAdditionalTabs)
    }
  }
}

const getIcon = (iconName: string) => {
  const icons = {
    'alert-triangle': AlertTriangle,
    'users': Users,
    'user': User,
    'file-text': FileText,
    'pill': Pill,
    'list': List,
    'shield': Shield,
    'life-buoy': LifeBuoy,
    'heart': Heart,
    'brain': Brain,
    'activity': Activity,
    'edit': Edit
  }
  
  return icons[iconName] || FileText
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}
</script>
