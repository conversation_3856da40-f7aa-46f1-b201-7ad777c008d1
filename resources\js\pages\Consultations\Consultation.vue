<template>
  <AppLayout>
    <Head :title="isEditing ? 'Edit Consultation' : 'New Consultation'" />

    <div class="min-h-screen bg-gray-50">
      <!-- Header - KiviCare Style -->
      <div class="bg-gradient-to-r from-pink-50 to-gray-50 border-b p-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <button @click="router.visit('/consultations')"
              class="flex items-center gap-2 px-3 py-1.5 bg-black text-white rounded text-sm hover:bg-gray-800">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                <path d="m12 19-7-7 7-7"/>
                <path d="M19 12H5"/>
              </svg>
              Back
            </button>
            <div v-if="consultation?.patient" class="flex items-center gap-3">
              <div class="bg-black rounded-lg p-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                  <circle cx="12" cy="7" r="4"/>
                </svg>
              </div>
              <div>
                <h1 class="font-medium text-black">
                  {{ consultation.patient.user?.name || 'Unknown Patient' }}
                </h1>
                <p class="text-sm text-gray-600">
                  {{ formatDate(consultation.consultation_date) }}
                </p>
              </div>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <button class="flex items-center gap-2 px-3 py-1.5 bg-white border rounded text-sm hover:bg-gray-50">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                <polyline points="6 9 6 2 18 2 18 9"/>
                <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"/>
                <rect width="12" height="8" x="6" y="14"/>
              </svg>
              Print
            </button>
            <button @click="saveConsultation" :disabled="saving"
              class="flex items-center gap-2 px-3 py-1.5 bg-black text-white rounded text-sm hover:bg-gray-800">
              <svg v-if="saving" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 animate-spin">
                <path d="M21 12a9 9 0 1 1-6.219-8.56"/>
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
                <polyline points="17 21 17 13 7 13 7 21"/>
                <polyline points="7 3 7 8 15 8"/>
              </svg>
              {{ saving ? 'Saving...' : 'Save All' }}
            </button>
            <button class="flex items-center gap-2 px-3 py-1.5 bg-black text-white rounded text-sm hover:bg-gray-800">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                <path d="M8 2v4"/>
                <path d="M16 2v4"/>
                <rect width="18" height="18" x="3" y="4" rx="2"/>
                <path d="M3 10h18"/>
              </svg>
              Schedule Follow-up
            </button>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="templateLoading" class="flex items-center justify-center py-12">
        <Loader2 class="w-8 h-8 animate-spin text-blue-600" />
        <span class="ml-2 text-gray-600">Loading consultation template...</span>
      </div>

      <!-- Main Content - KiviCare Style 3-column grid -->
      <div v-else class="p-3">
        <div class="grid grid-cols-3 gap-3">
          <!-- Left 2 Columns - Main Content -->
          <div class="col-span-2">
            <!-- Vital Signs -->
            <VitalSignsCard
              :vital-signs="consultationData.vital_signs"
              :template="template?.vital_signs"
              @update="updateVitalSigns"
            />

            <!-- Main Consultation Forms -->
            <div class="grid grid-cols-1 gap-3 mb-3">
              <ConsultationMainTabs
                :main-tabs="consultationData.main_tabs"
                :template="template?.main_tabs"
                @update="updateMainTabs"
                @add-entry="addMainTabEntry"
                @remove-entry="removeMainTabEntry"
              />
            </div>

            <!-- Additional Tabs Toggle Buttons -->
            <div class="mb-3">
              <ConsultationAdditionalTabs
                :additional-tabs="consultationData.additional_tabs"
                :template="template?.additional_tabs"
                @update="updateAdditionalTabs"
                @add-tab="addAdditionalTab"
                @remove-tab="removeAdditionalTab"
                @add-entry="addAdditionalTabEntry"
                @remove-entry="removeAdditionalTabEntry"
              />
            </div>

            <!-- Prescription Management -->
            <div v-if="consultationId" class="py-2">
              <div class="bg-white rounded border p-3">
                <div class="flex justify-between items-center mb-2">
                  <h2 class="font-medium">Prescriptions</h2>
                  <button @click="showPrescriptionModal = true"
                    class="text-blue-500 hover:text-blue-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                      <path d="M5 12h14"/>
                      <path d="M12 5v14"/>
                    </svg>
                  </button>
                </div>
                <div class="text-sm text-gray-500">
                  Click + to add prescriptions for this consultation
                </div>
              </div>
            </div>
          </div>

          <!-- Right Column - Sidebar -->
          <div class="col-span-1">
            <!-- Recent Vitals -->
            <RecentActivityCard :consultation="consultation" />

            <!-- Previous Visits -->
            <PreviousVisitsCard :patient-id="consultation?.patient_id" />

            <!-- File Management -->
            <div v-if="consultationId" class="bg-white rounded border p-3 mb-3">
              <div class="flex justify-between items-center mb-2">
                <h2 class="font-medium">Files</h2>
                <button @click="showDocumentModal = true"
                  class="text-blue-500 hover:text-blue-600">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                    <path d="M5 12h14"/>
                    <path d="M12 5v14"/>
                  </svg>
                </button>
              </div>
              <div class="text-sm text-gray-500">
                Upload documents, images, or files
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <PrescriptionModal
      v-if="showPrescriptionModal"
      :consultation-id="consultationId"
      @close="showPrescriptionModal = false"
      @saved="loadConsultation"
    />

    <DocumentUploadModal
      v-if="showDocumentModal"
      :consultation-id="consultationId"
      @close="showDocumentModal = false"
      @uploaded="loadConsultation"
    />
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import {
  ArrowLeft, FileText, Printer, User, Loader2
} from 'lucide-vue-next'

// Import consultation components
import VitalSignsCard from '@/components/consultation/VitalSignsCard.vue'
import ConsultationMainTabs from '@/components/consultation/ConsultationMainTabs.vue'
import ConsultationAdditionalTabs from '@/components/consultation/ConsultationAdditionalTabs.vue'
import PrescriptionModal from '@/components/consultation/PrescriptionModal.vue'
import DocumentUploadModal from '@/components/consultation/DocumentUploadModal.vue'
import QuickActionsCard from '@/components/consultation/QuickActionsCard.vue'
import RecentActivityCard from '@/components/consultation/RecentActivityCard.vue'
import PreviousVisitsCard from '@/components/consultation/PreviousVisitsCard.vue'

// Import composables
import { useConsultationTemplate } from '@/composables/useConsultationTemplate'
import { useConsultationApi } from '@/composables/useConsultationApi'
import axios from 'axios'

interface Props {
  consultationId?: string
  appointmentId?: string
  patientId?: string
}

const props = defineProps<Props>()

// Template and consultation data
const {
  template,
  loading: templateLoading,
  loadTemplate,
  initializeConsultationData,
  addTabEntry,
  removeTabEntry,
  calculateBMI,
  validateConsultationData
} = useConsultationTemplate()

const {
  getConsultation,
  updateConsultation,
  createConsultation
} = useConsultationApi()

// Component state
const consultation = ref(null)
const consultationData = ref({
  vital_signs: {},
  main_tabs: {},
  additional_tabs: {}
})
const saving = ref(false)
const showPrescriptionModal = ref(false)
const showDocumentModal = ref(false)

// Computed
const isEditing = computed(() => !!props.consultationId)

// Methods
const loadConsultation = async () => {
  if (props.consultationId) {
    try {
      const response = await getConsultation(props.consultationId)
      consultation.value = response.data
      consultationData.value = {
        vital_signs: response.data.vital_signs || {},
        main_tabs: response.data.main_tabs || {},
        additional_tabs: response.data.additional_tabs || {}
      }
    } catch (error) {
      console.error('Error loading consultation:', error)
    }
  } else if (props.appointmentId) {
    try {
      // Load appointment data to pre-populate consultation
      const response = await axios.get(`/api/appointments/${props.appointmentId}`)
      const appointment = response.data.data

      // Create consultation structure with appointment data
      consultation.value = {
        appointment_id: appointment.id,
        patient: appointment.patient,
        provider: appointment.provider,
        consultation_date: appointment.appointment_date,
        consultation_mode: appointment.appointment_type === 'video' ? 'video' : 'in_person',
        is_telemedicine: appointment.appointment_type === 'video'
      }

      consultationData.value = initializeConsultationData()
    } catch (error) {
      console.error('Error loading appointment:', error)
      consultationData.value = initializeConsultationData()
    }
  } else if (props.patientId) {
    try {
      // Load patient data to pre-populate consultation (KiviCare style)
      const response = await axios.get(`/api/patients/${props.patientId}`)
      const patient = response.data.data

      // Create consultation structure with patient data
      consultation.value = {
        patient: patient,
        consultation_date: new Date().toISOString().split('T')[0],
        consultation_mode: 'in_person',
        is_telemedicine: false
      }

      consultationData.value = initializeConsultationData()
    } catch (error) {
      console.error('Error loading patient:', error)
      consultationData.value = initializeConsultationData()
    }
  } else {
    // Initialize new consultation
    consultationData.value = initializeConsultationData()
  }
}

const saveConsultation = async () => {
  try {
    saving.value = true

    // Calculate BMI if height and weight are provided
    calculateBMI(consultationData.value)

    // Validate data
    const errors = validateConsultationData(consultationData.value)
    if (errors.length > 0) {
      alert('Please fix the following errors:\n' + errors.join('\n'))
      return
    }

    const payload = {
      ...consultationData.value,
      status: 'in_progress',
      // Include appointment data if creating from appointment
      ...(props.appointmentId && {
        appointment_id: props.appointmentId,
        patient_id: consultation.value?.patient?.id,
        consultation_date: consultation.value?.consultation_date,
        consultation_mode: consultation.value?.consultation_mode,
        is_telemedicine: consultation.value?.is_telemedicine
      }),
      // Include patient data if creating from patient (KiviCare style)
      ...(props.patientId && {
        patient_id: props.patientId,
        consultation_date: consultation.value?.consultation_date,
        consultation_mode: consultation.value?.consultation_mode,
        is_telemedicine: consultation.value?.is_telemedicine
      })
    }

    if (isEditing.value) {
      await updateConsultation(props.consultationId, payload)
    } else {
      const response = await createConsultation(payload)
      router.visit(`/consultations/${response.data.id}/edit`)
    }
  } catch (error) {
    console.error('Error saving consultation:', error)
  } finally {
    saving.value = false
  }
}

// Helper methods
const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

const calculateAge = (birthDate) => {
  if (!birthDate) return 'Unknown'
  const today = new Date()
  const birth = new Date(birthDate)
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }
  return age
}

// Event handlers for child components
const updateVitalSigns = (vitalSigns) => {
  consultationData.value.vital_signs = vitalSigns
}

const updateMainTabs = (mainTabs) => {
  consultationData.value.main_tabs = mainTabs
}

const updateAdditionalTabs = (additionalTabs) => {
  consultationData.value.additional_tabs = additionalTabs
}

const addMainTabEntry = (tabKey, content) => {
  addTabEntry(consultationData.value, 'main_tabs', tabKey, content)
}

const removeMainTabEntry = (tabKey, entryId) => {
  removeTabEntry(consultationData.value, 'main_tabs', tabKey, entryId)
}

const addAdditionalTab = (tabKey) => {
  if (!consultationData.value.additional_tabs[tabKey]) {
    consultationData.value.additional_tabs[tabKey] = []
  }
}

const removeAdditionalTab = (tabKey) => {
  delete consultationData.value.additional_tabs[tabKey]
}

const addAdditionalTabEntry = (tabKey, content) => {
  addTabEntry(consultationData.value, 'additional_tabs', tabKey, content)
}

const removeAdditionalTabEntry = (tabKey, entryId) => {
  removeTabEntry(consultationData.value, 'additional_tabs', tabKey, entryId)
}

// Initialize
onMounted(async () => {
  await loadTemplate()
  await loadConsultation()
})
</script>
