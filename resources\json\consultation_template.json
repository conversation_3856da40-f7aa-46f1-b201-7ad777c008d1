{"vital_signs": {"temperature": {"key": "temperature", "type": "number", "unit": "°F", "label": "Temperature", "placeholder": "98.6", "required": false, "min": 90, "max": 110}, "blood_pressure_systolic": {"key": "blood_pressure_systolic", "type": "number", "unit": "mmHg", "label": "Blood Pressure (Systolic)", "placeholder": "120", "required": false, "min": 70, "max": 250}, "blood_pressure_diastolic": {"key": "blood_pressure_diastolic", "type": "number", "unit": "mmHg", "label": "Blood Pressure (Diastolic)", "placeholder": "80", "required": false, "min": 40, "max": 150}, "heart_rate": {"key": "heart_rate", "type": "number", "unit": "bpm", "label": "Heart Rate", "placeholder": "72", "required": false, "min": 30, "max": 200}, "respiratory_rate": {"key": "respiratory_rate", "type": "number", "unit": "breaths/min", "label": "Respiratory Rate", "placeholder": "16", "required": false, "min": 8, "max": 40}, "oxygen_saturation": {"key": "oxygen_saturation", "type": "number", "unit": "%", "label": "Oxygen Saturation", "placeholder": "98", "required": false, "min": 70, "max": 100}, "weight": {"key": "weight", "type": "number", "unit": "kg", "label": "Weight", "placeholder": "70", "required": false, "min": 1, "max": 300}, "height": {"key": "height", "type": "number", "unit": "cm", "label": "Height", "placeholder": "175", "required": false, "min": 30, "max": 250}, "bmi": {"key": "bmi", "type": "number", "unit": "kg/m²", "label": "BMI", "placeholder": "22.9", "required": false, "calculated": true, "formula": "weight / (height/100)²"}}, "main_tabs": {"present_concerns": {"key": "present_concerns", "type": "textarea", "label": "Present Concerns", "placeholder": "What brings you in today?", "required": true, "multiple": true, "order": 1}, "present_history": {"key": "present_history", "type": "textarea", "label": "Present History", "placeholder": "History of present illness...", "required": false, "multiple": true, "order": 2}, "examination": {"key": "examination", "type": "textarea", "label": "Examination", "placeholder": "Physical examination findings...", "required": false, "multiple": true, "order": 3, "templates": ["CVS", "Resp", "Gastro", "Neur<PERSON>", "MSK", "Derm", "ENT"]}, "plan": {"key": "plan", "type": "textarea", "label": "Plan", "placeholder": "Treatment plan and next steps...", "required": false, "multiple": true, "order": 4}}, "additional_tabs": {"allergies": {"key": "allergies", "type": "textarea", "label": "Allergies", "placeholder": "Known allergies and reactions...", "required": false, "multiple": true, "icon": "alert-triangle"}, "family_history": {"key": "family_history", "type": "textarea", "label": "Family History", "placeholder": "Relevant family medical history...", "required": false, "multiple": true, "icon": "users"}, "social_history": {"key": "social_history", "type": "textarea", "label": "Social History", "placeholder": "Smoking, alcohol, occupation, etc...", "required": false, "multiple": true, "icon": "user"}, "past_medical_history": {"key": "past_medical_history", "type": "textarea", "label": "Past Medical History", "placeholder": "Previous medical conditions and surgeries...", "required": false, "multiple": true, "icon": "file-text"}, "medications": {"key": "medications", "type": "textarea", "label": "Current Medications", "placeholder": "Current medications and dosages...", "required": false, "multiple": true, "icon": "pill"}, "systems_review": {"key": "systems_review", "type": "textarea", "label": "Systems Review", "placeholder": "Review of systems...", "required": false, "multiple": true, "icon": "list"}, "safeguarding": {"key": "safeguarding", "type": "textarea", "label": "Safeguarding", "placeholder": "Safeguarding concerns or notes...", "required": false, "multiple": true, "icon": "shield"}, "safety_netting": {"key": "safety_netting", "type": "textarea", "label": "Safety Netting", "placeholder": "Safety netting advice given...", "required": false, "multiple": true, "icon": "life-buoy"}, "preventative_care": {"key": "preventative_care", "type": "textarea", "label": "Preventative Care", "placeholder": "Preventative care recommendations...", "required": false, "multiple": true, "icon": "heart"}, "mental_health": {"key": "mental_health", "type": "textarea", "label": "Mental Health", "placeholder": "Mental health assessment...", "required": false, "multiple": true, "icon": "brain"}, "lifestyle": {"key": "lifestyle", "type": "textarea", "label": "Lifestyle", "placeholder": "Lifestyle factors and advice...", "required": false, "multiple": true, "icon": "activity"}, "notes": {"key": "notes", "type": "textarea", "label": "Additional Notes", "placeholder": "Any additional notes...", "required": false, "multiple": true, "icon": "edit"}}}