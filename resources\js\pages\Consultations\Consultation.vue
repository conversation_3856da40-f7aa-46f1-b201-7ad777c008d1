<template>
  <AppLayout>
    <Head :title="isEditing ? 'Edit Consultation' : 'New Consultation'" />
    
    <DynamicConsultationForm :consultation-id="consultationId" />
  </AppLayout>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import DynamicConsultationForm from '@/components/consultation/DynamicConsultationForm.vue'

interface Props {
  consultationId?: string
  appointmentId?: string
}

const props = defineProps<Props>()

// Computed
const isEditing = computed(() => !!props.consultationId)
</script>
