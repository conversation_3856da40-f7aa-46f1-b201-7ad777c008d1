<template>
  <!-- KiviCare Style Vital Signs -->
  <div class="bg-white border rounded-lg shadow-sm mb-3">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-pink-50 to-gray-50 p-4 border-b">
      <div class="flex justify-between items-center">
        <div class="flex items-center gap-3">
          <div class="bg-black rounded-lg p-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
              <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
            </svg>
          </div>
          <div class="flex items-center gap-2">
            <h2 class="font-medium text-black">Vital Signs</h2>
          </div>
        </div>
        <div class="flex gap-2">
          <button @click="addVitalSet" class="p-2 text-pink-500 hover:bg-pink-50 rounded-lg transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
              <path d="M5 12h14"/>
              <path d="M12 5v14"/>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Vitals Grid -->
    <div class="p-4">
      <div class="grid grid-cols-5 gap-4">
        <div v-for="(field, key) in template" :key="key" class="relative group">
          <div class="p-4 bg-white border rounded-lg group-hover:border-pink-200 transition-colors">
            <div class="flex items-center justify-between mb-2">
              <label class="text-sm font-medium text-gray-600">{{ field.label }}</label>
              <div class="h-6 w-6 rounded-full bg-pink-50 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-pink-500" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2">
                  <path v-if="key === 'temperature'" d="M14 14.76V3.5a2.5 2.5 0 0 0-5 0v11.26a4.5 4.5 0 1 0 5 0z"/>
                  <path v-else-if="key === 'heart_rate'" d="M22 12h-4l-3 9L9 3l-3 9H2"/>
                  <circle v-else-if="key === 'blood_pressure_systolic' || key === 'blood_pressure_diastolic'" cx="12" cy="12" r="10"/>
                  <path v-else d="M12 2v8"/>
                </svg>
              </div>
            </div>

            <div class="relative">
              <input
                :type="field.type"
                :placeholder="field.placeholder"
                :min="field.min"
                :max="field.max"
                :value="vitalSigns[key] || ''"
                @input="updateVitalSign(key, $event.target.value)"
                class="w-full text-lg font-semibold bg-transparent border-0 focus:outline-none focus:ring-0 p-0"
              />
              <div v-if="field.unit" class="text-xs text-gray-400 mt-1">{{ field.unit }}</div>
            </div>

            <!-- Validation feedback -->
            <div v-if="getValidationMessage(key, vitalSigns[key])" class="text-xs text-red-500 mt-1">
              {{ getValidationMessage(key, vitalSigns[key]) }}
            </div>
          </div>
        </div>
      </div>


      <!-- BMI Display (if calculated) -->
      <div v-if="calculatedBMI" class="mt-4 p-3 bg-pink-50 rounded-lg">
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-700">BMI</span>
          <span class="text-lg font-bold" :class="getBMIClass(calculatedBMI)">
            {{ calculatedBMI }}
          </span>
        </div>
        <div class="text-xs text-gray-600 mt-1">
          {{ getBMICategory(calculatedBMI) }}
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="flex justify-between items-center mt-4 pt-3 border-t">
        <button @click="clearAll" class="text-sm text-gray-500 hover:text-gray-600">
          Clear All
        </button>
        <button @click="copyFromPrevious" class="text-sm text-gray-500 hover:text-gray-600">
          Copy Previous
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'

interface Props {
  vitalSigns: Record<string, any>
  template: Record<string, any>
}

interface Emits {
  (e: 'update', vitalSigns: Record<string, any>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Computed BMI
const calculatedBMI = computed(() => {
  const weight = parseFloat(props.vitalSigns?.weight || '0')
  const height = parseFloat(props.vitalSigns?.height || '0')
  
  if (weight > 0 && height > 0) {
    const heightInMeters = height / 100
    const bmi = weight / (heightInMeters * heightInMeters)
    return bmi.toFixed(1)
  }
  return null
})

// Watch for BMI calculation and auto-update
watch(calculatedBMI, (newBMI) => {
  if (newBMI) {
    updateVitalSign('bmi', newBMI)
  }
})

// Methods
const updateVitalSign = (key: string, value: string) => {
  const updatedVitalSigns = { ...props.vitalSigns }
  updatedVitalSigns[key] = value
  emit('update', updatedVitalSigns)
}

const getValidationMessage = (key: string, value: string) => {
  if (!value || !props.template[key]) return ''
  
  const field = props.template[key]
  const numValue = parseFloat(value)
  
  if (isNaN(numValue)) return ''
  
  if (field.min !== undefined && numValue < field.min) {
    return `Minimum value is ${field.min}${field.unit || ''}`
  }
  
  if (field.max !== undefined && numValue > field.max) {
    return `Maximum value is ${field.max}${field.unit || ''}`
  }
  
  return ''
}

const getBMIClass = (bmi: string) => {
  const bmiValue = parseFloat(bmi)
  if (bmiValue < 18.5) return 'text-blue-600'
  if (bmiValue < 25) return 'text-green-600'
  if (bmiValue < 30) return 'text-yellow-600'
  return 'text-red-600'
}

const getBMICategory = (bmi: string) => {
  const bmiValue = parseFloat(bmi)
  if (bmiValue < 18.5) return 'Underweight'
  if (bmiValue < 25) return 'Normal weight'
  if (bmiValue < 30) return 'Overweight'
  return 'Obese'
}

const addVitalSet = () => {
  // Could implement multiple vital sign sets here
  console.log('Add vital set functionality')
}

const clearAll = () => {
  const clearedVitalSigns = {}
  Object.keys(props.template).forEach(key => {
    clearedVitalSigns[key] = ''
  })
  emit('update', clearedVitalSigns)
}

const copyFromPrevious = () => {
  // Could implement copying from previous consultation
  console.log('Copy from previous functionality')
}
</script>
