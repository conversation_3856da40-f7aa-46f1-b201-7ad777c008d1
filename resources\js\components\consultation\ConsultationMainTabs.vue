<template>
  <div class="space-y-6">
    <!-- Main Tabs -->
    <div v-for="(field, key) in sortedMainTabs" :key="key">
      <Card>
        <CardHeader>
          <CardTitle class="text-lg flex items-center justify-between">
            {{ field.label }}
            <Button variant="ghost" size="sm" @click="addEntry(key)">
              <Plus class="w-4 h-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <!-- Existing Entries -->
          <div v-if="mainTabs[key]?.length" class="space-y-3">
            <div 
              v-for="(entry, index) in mainTabs[key]" 
              :key="entry.id"
              class="relative group"
            >
              <div class="flex items-start space-x-3">
                <div class="flex-1">
                  <Textarea
                    :placeholder="field.placeholder"
                    :value="entry.content"
                    @input="updateEntry(key, entry.id, $event.target.value)"
                    class="min-h-[100px] resize-none"
                    :required="field.required && index === 0"
                  />
                  <div class="text-xs text-gray-500 mt-1">
                    {{ formatDate(entry.created_at) }}
                  </div>
                </div>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  @click="removeEntry(key, entry.id)"
                  class="opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <X class="w-4 h-4 text-red-500" />
                </Button>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="text-center py-8 text-gray-500">
            <div class="text-sm">{{ field.placeholder }}</div>
            <Button variant="outline" size="sm" @click="addEntry(key)" class="mt-2">
              <Plus class="w-4 h-4 mr-1" />
              Add {{ field.label }}
            </Button>
          </div>

          <!-- Templates for Examination -->
          <div v-if="key === 'examination' && field.templates" class="mt-4">
            <Label class="text-sm font-medium">Quick Templates</Label>
            <div class="flex flex-wrap gap-2 mt-2">
              <Button
                v-for="template in field.templates"
                :key="template"
                variant="outline"
                size="sm"
                @click="addTemplateEntry(key, template)"
              >
                {{ template }}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Plus, X } from 'lucide-vue-next'

interface TabEntry {
  id: number
  content: string
  created_at: string
}

interface Props {
  mainTabs: Record<string, TabEntry[]>
  template: Record<string, any>
}

interface Emits {
  (e: 'update', mainTabs: Record<string, TabEntry[]>): void
  (e: 'add-entry', tabKey: string, content: string): void
  (e: 'remove-entry', tabKey: string, entryId: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Computed
const sortedMainTabs = computed(() => {
  if (!props.template) return {}
  
  return Object.fromEntries(
    Object.entries(props.template).sort(([, a], [, b]) => (a.order || 0) - (b.order || 0))
  )
})

// Methods
const addEntry = (tabKey: string) => {
  emit('add-entry', tabKey, '')
}

const removeEntry = (tabKey: string, entryId: number) => {
  emit('remove-entry', tabKey, entryId)
}

const updateEntry = (tabKey: string, entryId: number, content: string) => {
  const updatedMainTabs = { ...props.mainTabs }
  if (updatedMainTabs[tabKey]) {
    const entry = updatedMainTabs[tabKey].find(e => e.id === entryId)
    if (entry) {
      entry.content = content
      emit('update', updatedMainTabs)
    }
  }
}

const addTemplateEntry = (tabKey: string, template: string) => {
  const templateContent = getTemplateContent(template)
  emit('add-entry', tabKey, templateContent)
}

const getTemplateContent = (template: string) => {
  const templates = {
    'CVS': 'Cardiovascular System:\n- Heart rate: \n- Blood pressure: \n- Heart sounds: \n- Peripheral pulses: \n- Edema: ',
    'Resp': 'Respiratory System:\n- Respiratory rate: \n- Oxygen saturation: \n- Chest inspection: \n- Auscultation: \n- Percussion: ',
    'Gastro': 'Gastrointestinal System:\n- Abdomen inspection: \n- Palpation: \n- Bowel sounds: \n- Liver: \n- Spleen: ',
    'Neuro': 'Neurological System:\n- Mental status: \n- Cranial nerves: \n- Motor function: \n- Sensory function: \n- Reflexes: ',
    'MSK': 'Musculoskeletal System:\n- Joint inspection: \n- Range of motion: \n- Muscle strength: \n- Deformities: \n- Gait: ',
    'Derm': 'Dermatological System:\n- Skin inspection: \n- Color: \n- Texture: \n- Lesions: \n- Rashes: ',
    'ENT': 'ENT System:\n- Ears: \n- Nose: \n- Throat: \n- Lymph nodes: \n- Thyroid: '
  }
  
  return templates[template] || `${template} examination findings:`
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}
</script>
