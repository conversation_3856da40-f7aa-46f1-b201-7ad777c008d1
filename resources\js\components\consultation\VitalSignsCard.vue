<template>
  <Card>
    <CardHeader>
      <CardTitle class="text-lg flex items-center justify-between">
        <div class="flex items-center">
          <Activity class="w-5 h-5 mr-2" />
          Vital Signs
        </div>
        <Button variant="ghost" size="sm" @click="addVitalSet">
          <Plus class="w-4 h-4" />
        </Button>
      </CardTitle>
    </CardHeader>
    <CardContent class="space-y-4">
      <!-- Vital Signs Grid -->
      <div class="grid grid-cols-1 gap-4">
        <div v-for="(field, key) in template" :key="key" class="space-y-2">
          <Label class="text-sm font-medium flex items-center justify-between">
            {{ field.label }}
            <span v-if="field.unit" class="text-xs text-gray-500">{{ field.unit }}</span>
          </Label>
          
          <!-- Number Input for vital signs -->
          <div class="relative">
            <Input
              :type="field.type"
              :placeholder="field.placeholder"
              :min="field.min"
              :max="field.max"
              :required="field.required"
              :value="vitalSigns[key] || ''"
              @input="updateVitalSign(key, $event.target.value)"
              class="pr-12"
            />
            <span v-if="field.unit" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-500">
              {{ field.unit }}
            </span>
          </div>
          
          <!-- Validation feedback -->
          <div v-if="getValidationMessage(key, vitalSigns[key])" class="text-xs text-red-500">
            {{ getValidationMessage(key, vitalSigns[key]) }}
          </div>
        </div>
      </div>

      <!-- BMI Display (if calculated) -->
      <div v-if="calculatedBMI" class="mt-4 p-3 bg-blue-50 rounded-lg">
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium">BMI</span>
          <span class="text-lg font-bold" :class="getBMIClass(calculatedBMI)">
            {{ calculatedBMI }}
          </span>
        </div>
        <div class="text-xs text-gray-600 mt-1">
          {{ getBMICategory(calculatedBMI) }}
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="flex space-x-2 mt-4">
        <Button variant="outline" size="sm" @click="clearAll">
          <X class="w-4 h-4 mr-1" />
          Clear
        </Button>
        <Button variant="outline" size="sm" @click="copyFromPrevious">
          <Copy class="w-4 h-4 mr-1" />
          Copy Previous
        </Button>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Activity, Plus, X, Copy } from 'lucide-vue-next'

interface Props {
  vitalSigns: Record<string, any>
  template: Record<string, any>
}

interface Emits {
  (e: 'update', vitalSigns: Record<string, any>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Computed BMI
const calculatedBMI = computed(() => {
  const weight = parseFloat(props.vitalSigns?.weight || '0')
  const height = parseFloat(props.vitalSigns?.height || '0')
  
  if (weight > 0 && height > 0) {
    const heightInMeters = height / 100
    const bmi = weight / (heightInMeters * heightInMeters)
    return bmi.toFixed(1)
  }
  return null
})

// Watch for BMI calculation and auto-update
watch(calculatedBMI, (newBMI) => {
  if (newBMI) {
    updateVitalSign('bmi', newBMI)
  }
})

// Methods
const updateVitalSign = (key: string, value: string) => {
  const updatedVitalSigns = { ...props.vitalSigns }
  updatedVitalSigns[key] = value
  emit('update', updatedVitalSigns)
}

const getValidationMessage = (key: string, value: string) => {
  if (!value || !props.template[key]) return ''
  
  const field = props.template[key]
  const numValue = parseFloat(value)
  
  if (isNaN(numValue)) return ''
  
  if (field.min !== undefined && numValue < field.min) {
    return `Minimum value is ${field.min}${field.unit || ''}`
  }
  
  if (field.max !== undefined && numValue > field.max) {
    return `Maximum value is ${field.max}${field.unit || ''}`
  }
  
  return ''
}

const getBMIClass = (bmi: string) => {
  const bmiValue = parseFloat(bmi)
  if (bmiValue < 18.5) return 'text-blue-600'
  if (bmiValue < 25) return 'text-green-600'
  if (bmiValue < 30) return 'text-yellow-600'
  return 'text-red-600'
}

const getBMICategory = (bmi: string) => {
  const bmiValue = parseFloat(bmi)
  if (bmiValue < 18.5) return 'Underweight'
  if (bmiValue < 25) return 'Normal weight'
  if (bmiValue < 30) return 'Overweight'
  return 'Obese'
}

const addVitalSet = () => {
  // Could implement multiple vital sign sets here
  console.log('Add vital set functionality')
}

const clearAll = () => {
  const clearedVitalSigns = {}
  Object.keys(props.template).forEach(key => {
    clearedVitalSigns[key] = ''
  })
  emit('update', clearedVitalSigns)
}

const copyFromPrevious = () => {
  // Could implement copying from previous consultation
  console.log('Copy from previous functionality')
}
</script>
