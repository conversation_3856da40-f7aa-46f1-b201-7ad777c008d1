<template>
  <AppLayout>
    <Head title="New Consultation" />

    <DynamicConsultationForm />
  </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import DynamicConsultationForm from '@/components/consultation/DynamicConsultationForm.vue'
</script>





const updateMainFormsWithAIData = (extractedData: any) => {
  const mainFormMappings = {
    'concerns': ['concerns', 'problems', 'present_concerns'],
    'history': ['history', 'present_history'],
    'examination': ['examination']
  }
  
  Object.entries(mainFormMappings).forEach(([mainFormType, aiDataFields]) => {
    const mainForm = activeFormTypes.value.find(form => form.type === mainFormType && form.isMainForm)
    
    if (mainForm) {
      for (const aiField of aiDataFields) {
        if (extractedData[aiField]?.trim()) {
          if (!mainForm.content || mainForm.content.trim().length < 10) {
            mainForm.content = extractedData[aiField].trim()
            unsavedChanges.value.forms[mainForm.instanceId] = true
            break
          }
        }
      }
    }
  })
}

const addTabsForExtractedData = (extractedData: any) => {
  const existingTabTypes = activeFormTypes.value.map(form => form.type)
  
  supplementaryForms.forEach(formConfig => {
    const formType = formConfig.type
    
    if (extractedData[formType]?.trim()) {
      if (existingTabTypes.includes(formType)) {
        // Update existing tab
        const existingTab = activeFormTypes.value.find(form => form.type === formType)
        if (existingTab && (!existingTab.content || existingTab.content.trim() === '')) {
          existingTab.content = extractedData[formType].trim()
          unsavedChanges.value.forms[existingTab.instanceId] = true
        }
      } else {
        // Create new tab
        const newTab = {
          type: formType,
          tabId: null,
          instanceId: generateInstanceId(),
          content: extractedData[formType].trim(),
          metadata: {},
          title: formConfig.title,
          icon: formConfig.icon,
          templates: formConfig.templates || [],
          isMainForm: false,
        }
        
        activeFormTypes.value.push(newTab)
        existingTabTypes.push(formType)
        unsavedChanges.value.forms[newTab.instanceId] = true
      }
    }
  })
}

const handleAIApproval = (formData: any) => {
  approvedAIInstances.value[formData.instanceId] = true
  
  const form = activeFormTypes.value.find(
    f => f.type === formData.type && f.instanceId === formData.instanceId
  )
  
  if (form) {
    saveTab(form, true)
  }
}

const handleVitalsUpdate = ({ instanceId, data }: any) => {
  vitalSignsData.value[instanceId] = data
  unsavedChanges.value.vitals[instanceId] = true
}

const handleVitalsSaveSuccess = ({ instanceId, data, id }: any) => {
  vitalSignsData.value[instanceId] = { ...data, id }
  delete unsavedChanges.value.vitals[instanceId]
  lastSaved.value = new Date()
}

const handleVitalsClone = () => {
  const newId = `vitals-${vitalSignsInstances.value.length + 1}`
  vitalSignsInstances.value.push({ id: newId })
}

const handleVitalsRemove = (instanceId: string) => {
  const index = vitalSignsInstances.value.findIndex(instance => instance.id === instanceId)
  if (index !== -1) {
    vitalSignsInstances.value.splice(index, 1)
    delete vitalSignsData.value[instanceId]
  }
}

const handlePatientUpdated = (updatedDetails: any) => {
  patientDetails.value = updatedDetails
}

const handleScheduleFollowup = (appointmentData: any) => {
  console.log('Scheduling follow-up:', appointmentData)
  showFollowupModal.value = false
  toast.success('Follow-up scheduled successfully')
}

const scheduleFollowUp = () => {
  showFollowupModal.value = true
}

const completeConsultation = async () => {
  if (!(await showDeleteConfirm('this consultation', 'consultation', 'Complete', 'Cancel'))) return
  
  try {
    const response = await fetch('/api/consultations/status/update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
      body: JSON.stringify({
        id: consultationId.value,
        status: 'completed',
      }),
    })
    
    const data = await response.json()
    
    if (data.status) {
      if (consultationData.value) {
        consultationData.value.status = 'completed'
      }
      toast.success('Consultation completed successfully')
    } else {
      throw new Error(data.message || 'Failed to complete consultation')
    }
    
  } catch (error) {
    console.error('Error completing consultation:', error)
    toast.error('Failed to complete consultation')
  }
}

const handleBack = async () => {
  const hasUnsavedChanges =
    Object.keys(unsavedChanges.value.vitals).length > 0 ||
    Object.keys(unsavedChanges.value.forms).length > 0 ||
    unsavedChanges.value.plan

  if (hasUnsavedChanges) {
    if (!(await showDeleteConfirm('unsaved changes', 'changes', 'Discard', 'Stay'))) {
      return
    }
  }

  router.visit('/consultations')
}

// Lifecycle hooks
onMounted(() => {
  initializeMainForms()
  startTimer()
  
  if (consultationId.value) {
    fetchAllData()
  }
})

onUnmounted(() => {
  stopTimer()
})
</script>

<style scoped>
.gradient-bg {
  background: linear-gradient(45deg, rgba(248, 231, 255, 1) 0%, rgba(255, 255, 255, 0.9) 50%, rgba(248, 231, 255, 1) 100%);
  background-size: 200% 200%;
  animation: gradientMove 6s ease infinite;
}

@keyframes gradientMove {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}
</style>